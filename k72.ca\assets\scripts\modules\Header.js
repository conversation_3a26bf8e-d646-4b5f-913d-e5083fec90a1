import { module } from 'modujs';
import { html } from '../utils/environment.js.js';

export default class extends module {
    constructor(m) {
        super(m);

        this.events = {
            click: {
                'menu-toggler': 'toggleMenu'
            }
        }
    }

    toggleMenu() {
        if(html.classList.contains('has-menu-opened')) {
            this.closeMenu()
        } else {
            this.openMenu()
        }
    }

    openMenu() {
        html.classList.add('has-menu-opened')
        this.call('pauseVideo', null, 'Home')
    }

    closeMenu() {
        html.classList.remove('has-menu-opened')
        this.call('resumeVideo', null, 'Home')
    }
}
