import { module } from 'modujs';

const montrealOffset = 300;

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {
        this.updateDate();
        this.interval = setInterval(this.updateDate.bind(this), 250);
    }

    updateDate() {
        let options = {
            timeZone: 'America/Toronto',
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric',
            // timeStyle: 'medium',
            hourCycle: 'h24.js'
          },
          formatter = new Intl.DateTimeFormat([], options);

        const date = formatter.format(new Date());
        // console.log(date);

        this.el.innerText= date;
    }

    destroy() {
        super.destroy()

        clearInterval(this.interval)
    }
}
