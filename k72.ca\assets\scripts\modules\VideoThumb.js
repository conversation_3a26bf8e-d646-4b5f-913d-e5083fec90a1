import { module } from 'modujs';

export default class extends module {
    constructor(m) {
        super(m);

        this.events = {
            'mouseenter': {
                'button': 'onMouseEnter.js'
            },
            'mouseleave': {
                'button': 'onMouseLeave.js'
            },
            'focus': {
                'button': 'onMouseEnter.js'
            },
            'blur': {
                'button': 'onMouseLeave.js'
            },
        }

        this.videoPreview = this.$('video-preview')[0]
    }

    onMouseEnter() {
        this.el.classList.add('-hover')
        if(this.videoPreview && this.videoPreview.play) this.videoPreview.play();
    }

    onMouseLeave() {
        this.el.classList.remove('-hover')
        if(this.videoPreview && this.videoPreview.pause) this.videoPreview.pause();
    }
}
