import { module } from 'modujs';
import LocomotiveScroll from 'locomotive-scroll';
import { html } from '../utils/environment.js.js.js';
import { lazyLoadImage } from '../utils/image.js.js.js';

export default class extends module {
    constructor(m) {
        super(m);

        this.currentDirection = 'down.js'
    }

    init() {

        this.scroll = new LocomotiveScroll({
            el: this.el,
            smooth: true,
            getDirection: true
            // multiplier: 0.2
        });

        this.scroll.on('call', (func,way,obj,id) => {
            if(func[0] == 'themeUpdate') {
                if(way == 'enter')
                    html.setAttribute('data-theme', func[1])
            } else if(func[0] == 'infinite') {
                if(way == 'enter') {
                    if(!html.classList.contains('has-scroll-smooth')) return;

                    const scrollY = (this.scroll.scroll.instance.scroll.y + window.innerHeight) - obj.top
                    const deltaY = (this.scroll.scroll.instance.delta.y + window.innerHeight) - obj.top

                    this.scroll.scroll.instance = {
                        ...this.scroll.scroll.instance,
                        scroll: {
                            x: 0,
                            y: scrollY
                        },
                        delta: {
                            x: 0,
                            y: deltaY
                        }
                    }
                }
            } else {
                // Using modularJS
                this.call(func[0],{way,obj},func[1],func[2]);
            }
        });

        this.scroll.on('scroll', (args) => {
            if(args.direction != this.currentDirection) {
                html.setAttribute('data-scroll-direction', args.direction)
                this.currentDirection = args.direction;
            }

            if(args.currentElements['elastic-list']) {
                this.call('update', args.currentElements['elastic-list'].progress, 'ElasticList')
            }

            if(args.currentElements['about-hero-visual']) {
                this.call('update', args.currentElements['about-hero-visual'].progress, 'AboutHero')
            }

            if(args.scroll.y > 200) {
                html.classList.add('hide-quicknav')
            } else {
                html.classList.remove('hide-quicknav')
            }
        })

        if ('ResizeObserver' in window) {
            this.onResizeBind = this.update.bind(this);

            this.RO = new ResizeObserver(this.onResizeBind)

            this.RO.observe(document.body)
        }

        window.scroll = this

        this.modularimagesBind = () => {
            this.update()
        }
        window.addEventListener('modularimages', this.modularimagesBind)
    }

    /**
     * Lazy load the related image.
     *
     * @see ../utils/image.js
     *
     * It is recommended to wrap your `<img>` into an element with the
     * CSS class name `.c-lazy`. The CSS class name modifier `.-lazy-loaded`
     * will be applied on both the image and the parent wrapper.
     *
     * ```html
     * <div class="c-lazy o-ratio u-4:3">
     *     <img data-scroll data-scroll-call="lazyLoad, Scroll, main" data-src="http://picsum.photos/640/480?v=1" alt="" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" />
     * </div>
     * ```
     *
     * @param {LocomotiveScroll} args - The Locomotive Scroll instance.
     */
    lazyLoad(args) {
        lazyLoadImage(args.obj.el, null, () => {
            //callback
        })
    }

    scrollTo(options) {
        this.scroll.scrollTo(options.target, options);
    }

    update() {
        if (this.scroll && this.scroll.update) {
            this.scroll.update()
        }
    }

    destroy() {
        this.RO?.disconnect()
        this.scroll.destroy();
        window.removeEventListener('modularimages', this.modularimagesBind)
    }
}
