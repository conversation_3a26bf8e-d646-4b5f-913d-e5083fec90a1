<!DOCTYPE html>
<html>
<head>
    <title>Module Test</title>
</head>
<body>
    <h1>Testing ES6 Modules</h1>
    <div id="output"></div>
    
    <script type="module">
        try {
            console.log('Testing module imports...');
            
            // Test basic import
            import { html } from './assets/scripts/utils/environment.js';
            console.log('Environment import successful:', html);
            
            // Test modujs import
            import modular from './node_modules/modujs/dist/main.esm.js';
            console.log('Modujs import successful:', modular);
            
            // Test locomotive-scroll import
            import LocomotiveScroll from './node_modules/locomotive-scroll/dist/locomotive-scroll.esm.js';
            console.log('Locomotive Scroll import successful:', LocomotiveScroll);
            
            document.getElementById('output').innerHTML = 'All imports successful! Check console for details.';
            
        } catch (error) {
            console.error('Import error:', error);
            document.getElementById('output').innerHTML = 'Import error: ' + error.message;
        }
    </script>
</body>
</html>
