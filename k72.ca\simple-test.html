<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>K72.ca Website Test</h1>
    <div id="results"></div>
    
    <script>
        const results = document.getElementById('results');
        
        function addResult(test, success, message) {
            const div = document.createElement('div');
            div.className = `test ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${test}:</strong> ${message}`;
            results.appendChild(div);
        }
        
        // Test 1: Basic JavaScript
        addResult('Basic JavaScript', true, 'Working ✓');
        
        // Test 2: Check if CSS loaded
        const stylesheet = document.getElementById('stylesheet');
        if (stylesheet) {
            addResult('CSS Loading', true, 'Stylesheet link found ✓');
        } else {
            addResult('CSS Loading', false, 'Stylesheet link not found ✗');
        }
        
        // Test 3: Check if assets exist
        fetch('./assets/styles/main.css')
            .then(response => {
                if (response.ok) {
                    addResult('CSS File', true, 'CSS file accessible ✓');
                } else {
                    addResult('CSS File', false, 'CSS file not accessible ✗');
                }
            })
            .catch(err => {
                addResult('CSS File', false, 'CSS file error: ' + err.message);
            });
            
        // Test 4: Check if JS modules exist
        fetch('./assets/scripts/app.js')
            .then(response => {
                if (response.ok) {
                    addResult('JS File', true, 'JavaScript file accessible ✓');
                } else {
                    addResult('JS File', false, 'JavaScript file not accessible ✗');
                }
            })
            .catch(err => {
                addResult('JS File', false, 'JavaScript file error: ' + err.message);
            });
            
        // Test 5: Check if node_modules exist
        fetch('./node_modules/modujs/dist/main.esm.js')
            .then(response => {
                if (response.ok) {
                    addResult('ModuJS', true, 'ModuJS library accessible ✓');
                } else {
                    addResult('ModuJS', false, 'ModuJS library not accessible ✗');
                }
            })
            .catch(err => {
                addResult('ModuJS', false, 'ModuJS error: ' + err.message);
            });
            
        // Test 6: Check if locomotive-scroll exists
        fetch('./node_modules/locomotive-scroll/dist/locomotive-scroll.esm.js')
            .then(response => {
                if (response.ok) {
                    addResult('Locomotive Scroll', true, 'Locomotive Scroll library accessible ✓');
                } else {
                    addResult('Locomotive Scroll', false, 'Locomotive Scroll library not accessible ✗');
                }
            })
            .catch(err => {
                addResult('Locomotive Scroll', false, 'Locomotive Scroll error: ' + err.message);
            });
    </script>
</body>
</html>
