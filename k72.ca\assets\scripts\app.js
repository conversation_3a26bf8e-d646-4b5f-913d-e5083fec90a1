import modular from "modujs";
import * as modules from "./modules.js";
import { html } from "./utils/environment.js";
import { nestedRAF } from "./utils/html.js";

// ==========================================================================
// POLYFILLS
// ==========================================================================
CanvasRenderingContext2D.prototype.roundRect = function (x, y, w, h, r) {
  if (w < 2 * r) r = w / 2;
  if (h < 2 * r) r = h / 2;
  this.beginPath();
  this.moveTo(x + r, y);
  this.arcTo(x + w, y, x + w, y + h, r);
  this.arcTo(x + w, y + h, x, y + h, r);
  this.arcTo(x, y + h, x, y, r);
  this.arcTo(x, y, x + w, y, r);
  this.closePath();
  return this;
};
// ==========================================================================

const app = new modular({
  modules: modules,
});

window.onload = () => {
  const $style = document.getElementById("stylesheet");

  if ($style.isLoaded) {
    init();
  } else {
    $style.addEventListener("load", () => {
      init();
    });
  }
};

window.cursorPosition = {
  x: 0,
  y: 0,
};
window.addEventListener("mousemove", (e) => {
  window.cursorPosition.x = e.clientX;
  window.cursorPosition.y = e.clientY;
});

function init() {
  nestedRAF(() => {
    app.init(app);
    html.classList.add("is-loaded");
    html.classList.remove("is-loading");
    html.classList.remove("has-no-js");

    setTimeout(() => {
      html.classList.add("is-ready");
    }, 300);
  }, 7);
}
