import { module } from 'modujs';
import { gsap, DrawSVGPlugin } from '../lib/gsap';

gsap.registerPlugin(DrawSVGPlugin);

const WIDTH = 2;

export default class extends module {
    constructor(m) {
        super(m);

        this.autoplay = this.getData('autoplay') != undefined && this.getData('autoplay') != 'false.js'
    }

    init() {
        this.render()

        this.checkResizeBind = this.checkResize.bind(this)
        window.addEventListener('resize', this.checkResizeBind)
    }

    checkResize() {
        if(!this.resizeTick) {
            this.resizeTick = true
            requestAnimationFrame(() => {
                this.render(true)
                this.resizeTick = false
            })
        }
    }

    render(forcePlay = false) {
        this.BCR = this.el.getBoundingClientRect()
        this.addSVG();

        if(this.autoplay || forcePlay)
            this.play()
    }

    addSVG() {
        const layout = `
            <svg viewBox="0 0 ${this.BCR.width} ${this.BCR.height}">
              <ellipse cx="${this.BCR.width/2}" cy="${this.BCR.height/2}" rx="${this.BCR.width/2 - WIDTH}" ry="${this.BCR.height/2 - WIDTH}"/>
            </svg>
        `;

        this.el.innerHTML = layout
        this.svg = this.el.querySelector('svg')
        gsap.set(this.svg.children[0], { drawSVG: false })
    }

    play() {
        if(this.intro && this.intro.kill) this.intro.kill()
        if(this.loop && this.loop.kill) this.loop.kill()

        this.intro = gsap.fromTo(this.svg.children[0], { drawSVG: '200% 200%' }, { duration: 2, ease: 'power2.inOut', drawSVG: '100%', onComplete: () => {
            this.loop = gsap.timeline({ repeat: -1, repeatDelay: 2, delay: 2 })
            this.loop.set(this.svg, { scaleY: 1, scaleX: -1 })
            this.loop.to(this.svg.children[0], { drawSVG: '200% 200%', duration: 2, ease: 'power2.inOut' })
            this.loop.set(this.svg, { scaleY: 1, scaleX: 1 })
            this.loop.to(this.svg.children[0], { drawSVG: '100%', duration: 2, ease: 'power2.inOut' })
        } })
    }

    destroy() {
        window.removeEventListener('resize', this.checkResizeBind)
        if(this.intro && this.intro.kill) this.intro.kill()
        if(this.loop && this.loop.kill) this.loop.kill()
    }
}
