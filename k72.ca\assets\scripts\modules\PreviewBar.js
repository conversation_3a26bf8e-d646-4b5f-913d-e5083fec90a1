import { module } from 'modujs';
import { html } from '../utils/environment.js.js.js'

export default class extends module {
    constructor(m) {
        super(m);

        this.title = this.$('title')[0]
        this.subtitle = this.$('subtitle')[0]
        this.tag = this.$('tag')[0]
    }

    init() {

    }

    updateContent(content) {
        for(let field of ['title', 'subtitle', 'tag']) {
            let newHTML = `<span>${content[field]}</span>`

            if(newHTML != this[field].innerHTML)
                this[field].innerHTML = `<span>${content[field]}</span>`
        }
    }

    show() {
        html.classList.add('has-preview-bar-active')

        if(this.hasQuicknav == undefined) this.hasQuicknav = html.getAttribute('data-has-quicknav')
        html.setAttribute('data-has-quicknav', false)
    }

    hide(revertQuicknav = true) {
        html.classList.remove('has-preview-bar-active')
        if(revertQuicknav && this.hasQuicknav) html.setAttribute('data-has-quicknav', true)
    }

    destroy() {
        super.destroy();
        this.hide(false)
    }
}
