import { module } from 'modujs';
import { gsap } from '../lib/gsap';
import { html } from '../utils/environment.js.js'

export default class extends module {
    constructor(m) {
        super(m);

        // Main selectors
        this.items = Array.from(this.$('item'))
        this.inners = Array.from(this.$('inner'))
        this.visuals = Array.from(this.$('visual'))
    }

    init() {
        this.compute();

        this.checkResizeBind = this.checkResize.bind(this)
        window.addEventListener('resize', this.checkResizeBind)
    }

    checkResize() {
        if(!this.resizeTick) {
            this.resizeTick = true
            requestAnimationFrame(() => {
                this.compute()
                this.resizeTick = false
            })
        }
    }

    compute() {
        if(!html.classList.contains('has-scroll-smooth')) return;

        gsap.set([this.el, ...this.items, ...this.visuals, ...this.inners], { clearProps: 'all' });

        this.viewport = {
            width: window.innerWidth,
            height: window.innerHeight
        }

        this.elBCR = this.el.getBoundingClientRect(this.el)

        // COMPUTE ITEM SIZINGS & SPACINGS
        const marginBottom = parseInt(getComputedStyle(this.items[0]).getPropertyValue('margin-bottom'))
        const itemHeight = this.elBCR.width * 1/1.65
        const offsetTop = -(this.viewport.height - itemHeight) / 2;
        const offsetBottom = this.viewport.height - itemHeight / 2;

        // SET CONTAINER HEIGHT
        gsap.set(this.el, { height: this.elBCR.height - offsetTop });

        // FOR EACH ITEM APPLY TRANSFORMS TO OUTER, INNER & VISUAL FOR STICKY EFFECT TO WORK PROPERLY
        for(let i = 0; i < this.items.length; i++) {
            // Compute the height needed for the sticky area
            const height = (itemHeight+marginBottom) * (i+1) - offsetTop;

            // Apply outer (sticky area) styles
            gsap.set(this.items[i], {
                position: 'absolute',
                top: offsetTop,
                left: 0,
                width: '100%',
                height ,
                zIndex: this.items.length - i,
                // outline: `1px solid rgb(${Math.floor(Math.random()*256)},${Math.floor(Math.random()*256)},${Math.floor(Math.random()*256)})`
            })

            // Apply inner styles
            gsap.set(this.inners[i], {
                paddingTop: -offsetTop
            })

            // Apply visual styles (small transform offset for stacking effect)
            gsap.set(this.visuals[i], {
                scale: 1 - 0.025*i,
                y: 10 * i,
                transformOrigin: 'bottom center.js'
            })
        }

        // UPDATE THE SCROLL TO MATCH NEW LAYOUT (VERY IMPORTANT)
        this.call('update', null, 'Scroll')
    }

    destroy() {
        super.destroy();

        window.removeEventListener('resize', this.checkResizeBind)
    }
}
