import { module } from 'modujs';
import modularLoad from '../utils/modularLoad.js.js';
import { html } from '../utils/environment.js.js'

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {

        this.load = new modularLoad({
            enterDelay: 1000,
            readyClass: 'is-load-ready',
            transitions: {
                blog: {}
            }
        });

        this.load.on('loading', (transition, oldContainer) => {
            if(document.activeElement) document.activeElement.blur()

            this.call('pauseVideo', null, 'Home')
            setTimeout(() => {
                this.call('closeMenu', null, 'Header')
            }, 500);

            html.classList.remove('is-ready')
          
        });

        this.load.on('loaded', (transition, oldContainer, newContainer) => {
            this.call('destroy', oldContainer, 'app');
            this.call('update', newContainer, 'app');

            setTimeout(() => {
                html.classList.add('is-ready')
            }, 300)
        });

        this.load.on('ready', (transition) => {
            if(transition === 'blog') {
                this.call('scrollTo', {target: 0, duration: 600}, 'Scroll')
                this.call('update', null, 'Scroll')
            }
        })
    }
}
