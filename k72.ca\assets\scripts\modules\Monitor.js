import { module } from 'modujs';
import { lerp } from '../utils/maths.js.js'

// TODO - <PERSON><PERSON> resize

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {
        this.cursorProgress = 1;
        this.video = this.$('source', document.body)[0]

        this.ctx = this.el.getContext('2d')

        this.compute()
        this.updateBind = this.update.bind(this)
        this.update()

        this.checkResizeBind = this.checkResize.bind(this)
        window.addEventListener('resize', this.checkResizeBind)
    }

    compute() {
        this.viewportWidth = window.innerWidth;
        this.viewportHeight = window.innerHeight;

        this.BCR = this.el.getBoundingClientRect()
        this.ctx.canvas.width = this.BCR.width
        this.ctx.canvas.height = this.BCR.height
    }

    checkResize() {
        if(!this.resizeTick) {
            this.resizeTick = true
            requestAnimationFrame(() => {
                this.compute()
                this.resizeTick = false
            })
        }
    }

    update() {
        this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height)

        this.ctxRatio = this.ctx.canvas.width / this.ctx.canvas.height
        this.videoRatio = this.video.videoWidth/this.video.videoHeight

        if(this.ctxRatio > this.videoRatio) { // context wider than video
            this.ctx.drawImage(this.video,
                0,
                -((this.ctx.canvas.width*(1/this.videoRatio)) - this.ctx.canvas.height)/2,
                this.ctx.canvas.width,
                this.ctx.canvas.width*(1/this.videoRatio)
            );
        } else {
            this.ctx.drawImage(this.video,
                -((this.ctx.canvas.height*this.videoRatio) - this.ctx.canvas.width)/2,
                0,
                this.ctx.canvas.height*this.videoRatio,
                this.ctx.canvas.height);
        }

        this.ctx.globalCompositeOperation = 'destination-in';

        this.ctx.roundRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height, this.ctx.canvas.height/2).fill();

        this.ctx.globalCompositeOperation = 'source-over';

        this.raf = requestAnimationFrame(this.updateBind)
    }

    destroy() {
        window.removeEventListener('resize', this.checkResizeBind)
        cancelAnimationFrame(this.raf)
    }
}
