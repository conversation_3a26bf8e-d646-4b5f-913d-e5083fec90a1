import { module } from 'modujs';
import { html } from '../utils/environment.js.js';

export default class extends module {
    constructor(m) {
        super(m);

        this.bar = this.getData('bar')
        this.items = Array.from(this.$('item'));
    }

    init() {
        if(window.matchMedia('(hover: none)').matches) {
            this.disabled = true
            return
        }

        this.onItemEnterBind = this.onItemEnter.bind(this)
        this.onItemLeaveBind = this.onItemLeave.bind(this)

        for(let item of this.items) {
            item.addEventListener('mouseenter', this.onItemEnterBind)
            item.addEventListener('mouseleave', this.onItemLeaveBind)
            item.addEventListener('focus', this.onItemEnterBind)
            item.addEventListener('blur', this.onItemLeaveBind)
        }
    }

    onItemEnter(e) {
        clearTimeout(this.leaveTimeout);

        let content = {}
        for(let field of ['title', 'subtitle', 'tag']) {
            content[field] = this.getData(field, e.currentTarget)
        }

        this.call('show', null, 'PreviewBar', this.bar)
        this.call('updateContent', content, 'PreviewBar', this.bar)
    }

    onItemLeave(e) {
        clearTimeout(this.leaveTimeout);
        this.leaveTimeout = setTimeout(() => {
            this.call('hide', undefined, 'PreviewBar', this.bar)
        }, 250)
    }

    destroy() {
        super.destroy()

        clearTimeout(this.leaveTimeout);

        for(let item of this.items) {
            item.removeEventListener('mouseenter', this.onItemEnterBind)
            item.removeEventListener('mouseleave', this.onItemLeaveBind)
            item.removeEventListener('focus', this.onItemEnterBind)
            item.removeEventListener('blur', this.onItemLeaveBind)
        }
    }
}
