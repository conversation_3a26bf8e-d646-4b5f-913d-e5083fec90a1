<!DOCTYPE html>
<html class="has-no-js is-loading" lang="fr" data-theme="dark" data-template="home" data-has-quicknav="false">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="x-ua-compatible" content="ie=edge" />

		<!-- <base href="https://k72.ca/"> -->

		<title>Accueil — Agence K72</title>

		<link rel="canonical" href="https://k72.ca/" />
		<link rel="alternate" hreflang="fr" href="https://k72.ca/" />
		<link rel="alternate" hreflang="en" href="https://k72.ca/en" />

		<meta property="og:type" content="website" />
		<meta property="og:url" content="https://k72.ca/" />
		<meta property="og:site_name" content="Agence K72" />
		<meta property="og:title" content="Accueil" />
		<meta property="og:image" content="https://k72.ca/assets/images/favicons/opengraph.png" />

		<!-- Viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
		<meta name="HandheldFriendly" content="true" />

		<!-- Appearance -->
		<link
			rel="apple-touch-icon"
			sizes="180x180"
			href="/assets/images/favicons/apple-touch-icon.png?v=20240527152606"
		/>
		<link
			rel="icon"
			type="image/png"
			sizes="32x32"
			href="/assets/images/favicons/favicon-32x32.png?v=20240527152606"
		/>
		<link
			rel="icon"
			type="image/png"
			sizes="16x16"
			href="/assets/images/favicons/favicon-16x16.png?v=20240527152606"
		/>
		<link rel="mask-icon" href="/assets/images/favicons/safari-pinned-tab.svg?v=20240527152606" color="#000000" />
		<link rel="manifest" href="/site.webmanifest" />
		<meta name="msapplication-TileColor" content="#000000" />
		<meta name="theme-color" content="#ffffff" />

		<!-- Fonts -->
		<link rel="preload" href="assets/fonts/Lausanne-300.woff2" as="font" type="font/woff2" crossorigin />
		<link rel="preload" href="assets/fonts/Lausanne-500.woff2" as="font" type="font/woff2" crossorigin />

		<!-- Styles -->
		<style media="all">
			html {
				min-height: 100%;
				color: #000;
				font-family: 'Lausanne';
				line-height: 1.5;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
				background-color: #fff;
				transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1),
					background-color 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
			}
			@media (max-width: 699px) {
				html {
					font-size: 14px;
				}
			}
			@media (min-width: 700px) and (max-width: 999px) {
				html {
					font-size: 14px;
				}
			}
			@media (min-width: 1000px) and (max-width: 1199px) {
				html {
					font-size: 15px;
				}
			}
			@media (min-width: 1200px) and (max-width: 1599px) {
				html {
					font-size: 16px;
				}
			}
			@media (min-width: 1600px) and (max-width: 1999px) {
				html {
					font-size: 17px;
				}
			}
			@media (min-width: 2000px) and (max-width: 2399px) {
				html {
					font-size: 18px;
				}
			}
			@media (min-width: 2400px) {
				html {
					font-size: 20px;
				}
			}
			html.is-loading {
				cursor: wait;
			}
			html.has-scroll-smooth {
				overflow: hidden;
			}
			html[data-theme='dark'] {
				background-color: #000;
				color: #fff;
			}
			.has-scroll-smooth body {
				overflow: hidden;
			}
			::-moz-selection {
				background-color: #d3fd50;
				color: #000;
				text-shadow: none;
			}
			::selection {
				background-color: #d3fd50;
				color: #000;
				text-shadow: none;
			}
			a {
				color: currentColor;
			}
			a:focus,
			a:hover {
				color: currentColor;
			}
			.c-loader {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				display: flex;
				z-index: 850;
				pointer-events: none;
			}
			.c-loader:before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 5;
				background-color: #1d1d1d;
				opacity: 0;
				transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
			}
			html:not(.is-loaded) .c-loader {
				pointer-events: all;
			}
			html:not(.is-loaded) .c-loader:before {
				opacity: 0.8;
			}
			.c-loader_col {
				position: relative;
				z-index: 10;
				background-color: #000;
				height: 100%;
				transform: scale3d(1, 0, 1);
				transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
				transform-origin: center bottom;
			}
			html:not(.is-loaded) .c-loader_col {
				transform: scale3d(1, 1, 1);
				transform-origin: center top;
			}
			@media (min-width: 1000px) {
				.c-loader_col {
					width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem + 2px);
					margin-left: -1px;
				}
				.c-loader_col:nth-child(3) {
					width: calc((100vw - 13.125rem) * 6 / 20 + 3.75rem + 2px);
				}
				.c-loader_col:nth-child(4) {
					width: calc((100vw - 13.125rem) * 3 / 20 + 1.875rem + 2px);
				}
				.c-loader_col:nth-child(5) {
					width: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem + 2px);
				}
				.c-loader_col:nth-child(5) {
					transition-delay: 0.075s;
				}
				.c-loader_col:nth-child(4) {
					transition-delay: 0.12s;
				}
				.c-loader_col:nth-child(3) {
					transition-delay: 0.165s;
				}
				.c-loader_col:nth-child(2) {
					transition-delay: 0.21s;
				}
				.c-loader_col:nth-child(1) {
					transition-delay: 0.255s;
				}
			}
			@media (max-width: 999px) {
				.c-loader_col {
					width: 33.3333333333%;
					width: 33.3333333333%;
					width: 33.3333333333%;
				}
				.c-loader_col:nth-child(2) {
					transition-delay: 0.075s;
				}
				.c-loader_col:nth-child(1) {
					transition-delay: 0.12s;
				}
				.c-loader_col:nth-child(0) {
					transition-delay: 0.165s;
				}
			}
			.c-loader_spinner {
				position: absolute;
				bottom: 0.625rem;
				right: 0.625rem;
				width: 3.125rem;
				height: 1.7578125rem;
				display: flex;
				transform: scaleY(0);
				transform-origin: center bottom;
				transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
				z-index: 20;
			}
			html:not(.is-loaded) .c-loader_spinner {
				transform: scaleY(1);
				transition-delay: 0.3s;
			}
			.c-loader_spinner div {
				flex-grow: 1;
				background-color: #fff;
				animation: loaderSpinnerCol 1s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
				margin-left: -1px;
			}
			.c-loader_spinner div:nth-child(1) {
				animation-delay: -0.1s;
			}
			.c-loader_spinner div:nth-child(2) {
				animation-delay: -0.2s;
			}
			.c-loader_spinner div:nth-child(3) {
				animation-delay: -0.3s;
			}
			.c-loader_spinner div:nth-child(4) {
				animation-delay: -0.4s;
			}
			@keyframes loaderSpinnerCol {
				0% {
					transform: scale3d(1, 0.25, 1);
					transform-origin: bottom center;
				}
				50% {
					transform: scale3d(1, 1, 1);
					transform-origin: bottom center;
				}
				100% {
					transform: scale3d(1, 0.25, 1);
					transform-origin: bottom center;
				}
			}
			.c-header_link {
				color: #fff;
			}
			.c-header_link span {
				text-transform: uppercase;
				font-size: 1.25rem;
				line-height: 0.9;
				opacity: 0;
				color: currentColor;
				z-index: 2;
			}
			.c-header_link:before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: #000;
				z-index: 0;
			}
			.c-header_link:after {
				content: '';
				background-color: #d3fd50;
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 101%;
				transform: translate3d(0, -102%, 0);
				transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
				z-index: 1;
			}
			@media (min-width: 1000px), (min-aspect-ratio: 1/1) {
				html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_link span {
					opacity: 1;
					transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s;
				}
			}
			.c-header_link:hover,
			.c-header_link:focus {
				color: #000;
			}
			.c-header_link:hover:after,
			.c-header_link:focus:after {
				transform: translate3d(0, 0, 0);
			}
			.c-header_logo {
				position: fixed;
				top: 0;
				left: 0;
				pointer-events: all;
				display: inline-block;
				padding: 0.625rem;
				color: inherit;
				z-index: 900;
				color: #000;
				transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
			}
			html[data-theme='dark'] .c-header_logo,
			html.has-menu-opened .c-header_logo,
			html.is-loading .c-header_logo {
				color: #fff;
			}
			.c-header_logo svg {
				fill: currentColor;
				display: inline-block;
				width: 7.3125rem;
				height: 3.125rem;
				transform-origin: top left;
				transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
			}
			html.has-preview-bar-active .c-header_logo svg {
				transform: scale3d(0.6, 0.6, 1);
			}
			.c-header_quicknav {
				position: fixed;
				top: 0;
				display: flex;
				z-index: 700;
				pointer-events: none;
			}
			@media (min-width: 1000px) {
				.c-header_quicknav {
					right: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem);
				}
			}
			@media (max-width: 999px) {
				.c-header_quicknav {
					right: 33.3333333333%;
				}
			}
			@media (max-width: 699px) {
				.c-header_quicknav {
					right: 50%;
				}
			}
			@media (max-width: 999px), (max-aspect-ratio: 1/1) {
				.c-header_quicknav {
					display: none;
				}
			}
			.c-header_quicknav_item {
				position: relative;
				display: flex;
				transform: translate3d(0, -100%, 0);
				transition-property: transform;
				transition-duration: 0.3s;
				transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
			}
			.c-header_quicknav_item:nth-child(1) {
				height: 3.4375rem;
				width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem);
			}
			.c-header_quicknav_item:nth-child(2) {
				height: 5.625rem;
				width: calc((100vw - 13.125rem) * 6 / 20 + 3.75rem);
			}
			html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_quicknav_item {
				transform: translate3d(0, 0, 0);
				transition-duration: 0.6s;
			}
			html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_quicknav_item:nth-child(1) {
				transition-delay: 0.3s;
			}
			html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_quicknav_item:nth-child(2) {
				transition-delay: 0.15s;
			}
			.c-header_quicknav_link {
				display: flex;
				width: 100%;
				padding: 0.3125rem 0.625rem;
				align-items: flex-end;
			}
			html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_quicknav_link {
				pointer-events: all;
			}
			.c-header_menu-btn {
				position: fixed;
				top: 0;
				right: 0;
				color: #fff;
				z-index: 700;
				height: 3.125rem;
				display: flex;
				padding-right: 1.5625rem;
				justify-content: flex-end;
				align-items: center;
				transition: color 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
			}
			@media (min-width: 1000px) {
				.c-header_menu-btn {
					width: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem);
				}
			}
			@media (max-width: 999px) {
				.c-header_menu-btn {
					width: 33.3333333333%;
				}
			}
			@media (max-width: 699px) {
				.c-header_menu-btn {
					width: 50%;
				}
			}
			.c-header_menu-btn svg {
				position: absolute;
				top: 1.5625rem;
				margin-top: -0.25rem;
				width: 3.8125rem;
				height: 0.5rem;
				z-index: 2;
				stroke: currentColor;
			}
			.c-header_menu-btn:before {
				height: 3.125rem;
				transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
				transform-origin: top center;
			}
			.c-header_menu-btn span {
				position: absolute;
				top: 2.8125rem;
				left: 0.625rem;
				transform: translate3d(0, -100%, 0);
			}
			@media (min-width: 1000px), (min-aspect-ratio: 1/1) {
				html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_menu-btn {
					height: 8.125rem;
				}
				html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_menu-btn span {
					transform: translate3d(0, calc(5rem - 100%), 0);
					opacity: 1;
					transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s,
						transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
				}
				html.is-ready[data-has-quicknav='true']:not(.hide-quicknav) .c-header_menu-btn:before {
					transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
					transform: scale3d(1, 2.6, 1);
				}
			}
			.u-z-index-2 {
				z-index: 2;
			}
			.u-indent {
				text-indent: 5em;
			}
			[hidden][aria-hidden='false'] {
				position: absolute;
				display: inherit;
				clip: rect(0, 0, 0, 0);
			}
			[hidden][aria-hidden='false']:focus {
				clip: auto;
			}
			.u-screen-reader-text {
				position: absolute !important;
				overflow: hidden;
				clip: rect(0 0 0 0);
				margin: 0;
				padding: 0;
				width: 1px;
				height: 1px;
				border: 0;
			}
			.c-menu,
			.c-text-lines,
			.c-about-hero_visual_outer,
			.c-video-modal {
				display: none;
			}
		</style>

		<link
			id="stylesheet"
			rel="stylesheet"
			href="assets/styles/main.css?v=20240527152606"
			media="print"
			onload="this.media='all'; this.onload=null; this.isLoaded=true"
		/>

		<!-- Didomi Privacy Script - Commented out for local development
		<script type="text/javascript">
			window.didomiConfig = {
				languages: {
					enabled: ['fr'],
				},
				user: {
					bots: {
						consentRequired: false,
						types: ['crawlers', 'performance'],
						extraUserAgents: [],
					},
				},
				theme: {
					color: '#D3FD50', // Principal color used by the SDK
					//linkColor: '#fff',
					buttons: {
						regularButtons: {
							// Learn more/disagree/disagree to all buttons.
							backgroundColor: '#ffffff',
							textColor: '#000000',
							borderColor: 'transparent',
							borderWidth: '0px',
							borderRadius: '0px',
						},
						highlightButtons: {
							// Agree/save/agree to all buttons.
							backgroundColor: '#D3FD50',
							textColor: '#000000',
							borderColor: 'transparent',
							borderWidth: '0px',
							borderRadius: '0px',
						},
					},
					css: `
                        #didomi-host .didomi-notice-banner {
                            background-color: #000 !important;
                            color: #fff !important;
                            border: none !important;
                        }
                        #didomi-notice .didomi-notice-text a {
                            color: #fff !important;
                        }
                        #didomi-host .didomi-mobile #didomi-notice.didomi-regular-notice.bottom .didomi-notice__interior-border {
                            border: none !important;
                        }
                        #didomi-host #didomi-notice.didomi-regular-notice.didomi-regular-notice-with-data-processing .didomi-notice-text,
                        #didomi-host .didomi-notice-data-processing-container p {
                            font-size: 12px;
                            line-height: 1.4;
                            font-weight: 800;
                        }
                        #didomi-host .didomi-notice-data-processing-container {
                            text-align: left;

                            .didomi-notice-data-processing-title,
                            .didomi-notice-data-processing-list {
                                color: #fff;
                            }
                        }
                        #didomi-host #didomi-notice.didomi-regular-notice.didomi-regular-notice-with-data-processing .didomi-notice-text b:first-child {
                            font-size: 2em;
                        }
                        #didomi-host #didomi-notice.didomi-regular-notice #buttons {
                            gap: 10px;
                        }
                        #didomi-host #didomi-notice.didomi-regular-notice.shape-banner #buttons.multiple button {
                            text-transform: uppercase;
                            margin: 0;
                        }
                        @media (max-width: 999px) {
                            #didomi-host #didomi-notice.didomi-regular-notice.shape-banner .didomi-notice__interior-border {
                                flex-direction: column;
                            }
                        }
                        @media (max-width: 699px) or (min-width: 1000px) {
                            #didomi-host #didomi-notice.didomi-regular-notice #buttons {
                                flex-direction: column;
                            }
                        }
                        @media (min-width: 1000px) {
                            #didomi-host #didomi-notice.didomi-regular-notice.shape-banner {
                                padding: 3em 2em;
                            }
                        }
                    `,
				},
			};

			window.gdprAppliesGlobally = false;
			(function () {
				(function (e, r) {
					var t = document.createElement('link');
					t.rel = 'preconnect';
					t.as = 'script';
					var n = document.createElement('link');
					n.rel = 'dns-prefetch';
					n.as = 'script';
					var i = document.createElement('link');
					i.rel = 'preload';
					i.as = 'script';
					var o = document.createElement('script');
					o.id = 'spcloader';
					o.type = 'text/javascript';
					o['async'] = true;
					o.charset = 'utf-8';
					var a = 'https://sdk.privacy-center.org/' + e + '/loader.js?target_type=notice&target=' + r;
					if (window.didomiConfig && window.didomiConfig.user) {
						var c = window.didomiConfig.user;
						var s = c.country;
						var d = c.region;
						if (s) {
							a = a + '&country=' + s;
							if (d) {
								a = a + '&region=' + d;
							}
						}
					}
					t.href = 'https://sdk.privacy-center.org/';
					n.href = 'https://sdk.privacy-center.org/';
					i.href = a;
					o.src = a;
					var p = document.getElementsByTagName('script')[0];
					p.parentNode.insertBefore(t, p);
					p.parentNode.insertBefore(n, p);
					p.parentNode.insertBefore(i, p);
					p.parentNode.insertBefore(o, p);
				})('3ec5dcd5-a18f-4d2e-ac51-3f323e2ab0c5', 'qBNmVFaa');
			})();

			window.didomiOnReady = window.didomiOnReady || [];
			window.didomiOnReady.push(function (Didomi) {});
		</script>
		-->

		<!-- <script id="6senseWebTag" async src="https://j.6sc.co/j/a640d4d0-f937-416d-ad10-140597105f47.js"></script> -->

		<!-- Google Tag Manager - Commented out for local development
		<script>
			(function (w, d, s, l, i) {
				w[l] = w[l] || [];
				w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
				var f = d.getElementsByTagName(s)[0],
					j = d.createElement(s),
					dl = l != 'dataLayer' ? '&l=' + l : '';
				j.async = true;
				j.src = ' https://www.googletagmanager.com/gtm.js?id=' + i + dl;
				f.parentNode.insertBefore(j, f);
			})(window, document, 'script', 'dataLayer', 'GTM-MWC9BML6');
		</script>
		-->
	</head>
	<body data-module-load>
		<!-- Enhanced Import map for ES6 modules -->
		<script type="importmap">
			{
				"imports": {
					"modujs": "./node_modules/modujs/dist/main.esm.js",
					"locomotive-scroll": "./node_modules/locomotive-scroll/dist/locomotive-scroll.esm.js",
					"gsap": "./node_modules/gsap/gsap-core.js",
					"gsap/all": "./node_modules/gsap/all.js",
					"gsap/DrawSVGPlugin": "./node_modules/gsap/DrawSVGPlugin.js",
					"gsap/SplitText": "./node_modules/gsap/SplitText.js"
				}
			}
		</script>

		<!-- Fallback for browsers that don't support import maps -->
		<script>
			// Check if import maps are supported
			if (!HTMLScriptElement.supports || !HTMLScriptElement.supports('importmap')) {
				console.warn('Import maps not supported, using fallback initialization');

				// Simple fallback initialization
				document.addEventListener('DOMContentLoaded', function () {
					const html = document.documentElement;
					html.classList.remove('is-loading');
					html.classList.add('is-loaded');

					setTimeout(() => {
						html.classList.add('is-ready');

						// Basic menu functionality
						const menuToggler = document.querySelector('[data-header="menu-toggler"]');
						const menu = document.querySelector('.c-menu');

						if (menuToggler && menu) {
							menuToggler.addEventListener('click', function () {
								html.classList.toggle('has-menu-opened');
							});
						}

						console.log('K72.ca website loaded with fallback functionality');
					}, 300);
				});
			}
		</script>
		<!-- Google Tag Manager noscript - Commented out for local development
		<noscript>
			<iframe
				src="https://www.googletagmanager.com/ns.html?id=GTM-MWC9BML6"
				height="0"
				width="0"
				style="display: none; visibility: hidden"
			></iframe>
		</noscript>
		-->

		<div class="c-loader">
			<div class="c-loader_spinner">
				<div></div>
				<div></div>
				<div></div>
				<div></div>
			</div>
			<div class="c-loader_col"><div></div></div>
			<div class="c-loader_col"><div></div></div>
			<div class="c-loader_col"><div></div></div>
			<div class="c-loader_col"><div></div></div>
			<div class="c-loader_col"><div></div></div>
		</div>

		<div data-load-container>
			<header class="c-header" data-module-header>
				<a class="c-header_logo" href="/" title="Aller à la page d'accueil">
					<div class="u-screen-reader-text">K72</div>
					<svg xmlns="http://www.w3.org/2000/svg" width="103" height="44" viewBox="0 0 103 44">
						<path
							fill-rule="evenodd"
							d="M35.1441047,8.4486911 L58.6905011,8.4486911 L58.6905011,-1.3094819e-14 L35.1441047,-1.3094819e-14 L35.1441047,8.4486911 Z M20.0019577,0.000230366492 L8.83414254,25.3433089 L18.4876971,25.3433089 L29.5733875,0.000230366492 L20.0019577,0.000230366492 Z M72.5255345,0.000691099476 L72.5255345,8.44846073 L94.3991559,8.44846073 L94.3991559,16.8932356 L72.5275991,16.8932356 L72.5275991,19.5237906 L72.5255345,19.5237906 L72.5255345,43.9274346 L102.80937,43.9274346 L102.80937,35.4798953 L80.9357483,35.4798953 L80.9357483,25.3437696 L94.3996147,25.3428482 L94.3996147,16.8953089 L102.80937,16.8953089 L102.80937,0.000691099476 L72.5255345,0.000691099476 Z M-1.30398043e-14,43.9278953 L8.78642762,43.9278953 L8.78642762,0.0057591623 L-1.30398043e-14,0.0057591623 L-1.30398043e-14,43.9278953 Z M58.6849955,8.4486911 L43.1186904,43.9274346 L52.3166592,43.9274346 L67.9877996,8.4486911 L58.6849955,8.4486911 Z M18.4688864,25.3437696 L26.7045278,43.9278953 L36.2761871,43.9278953 L28.1676325,25.3375497 L18.4688864,25.3437696 Z"
						/>
					</svg>
				</a>

				<ul class="c-header_quicknav">
					<li class="c-header_quicknav_item">
						<a class="c-header_quicknav_link || c-header_link" href="travail">
							<span>Projets (16)</span>
						</a>
					</li>
					<li class="c-header_quicknav_item">
						<a class="c-header_quicknav_link || c-header_link" href="agence">
							<span>Agence</span>
						</a>
					</li>
				</ul>

				<button class="c-header_menu-btn || c-header_link" data-header="menu-toggler" type="button">
					<span>Menu</span>
					<svg role="presentation" alt=""><use xlink:href="assets/images/sprite.svg#burger"></use></svg>
				</button>

				<nav class="c-menu">
					<div class="c-menu_bg">
						<div class="c-menu_bg_col"></div>
						<div class="c-menu_bg_col"></div>
						<div class="c-menu_bg_col"></div>
						<div class="c-menu_bg_col"></div>
						<div class="c-menu_bg_col"></div>
					</div>
					<div class="c-menu_inner">
						<button class="c-menu_close" data-header="menu-toggler">
							<span class="u-screen-reader-text">Fermer le menu</span>
							<svg role="presentation" alt="Close icon">
								<use xlink:href="assets/images/sprite.svg#close"></use>
							</svg>
						</button>

						<div class="c-menu_langswitcher">
							<span class="c-menu_langswitcher_item -active">fr</span>&nbsp;&nbsp;/&nbsp;&nbsp;<a
								href="https://k72.ca/en"
								data-load="false"
								class="c-menu_langswitcher_item -link"
								>en</a
							>
						</div>

						<ul class="c-menu_main-nav" data-module-main-nav>
							<li class="c-menu_main-nav_item">
								<a href="travail" class="c-menu_main-nav_link" data-main-nav="link">
									<span>Projets</span>
									<div class="c-menu_main-nav_link_overlay">
										<div class="c-menu_main-nav_link_overlay_inner">
											<div>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg');
													"
												></div>
												<span>Pour tout voir</span>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg');
													"
												></div>
												<span>Pour tout voir&nbsp;</span>
											</div>
											<div>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/caseStudies/PJC/Thumbnails/PJC_SiteK72_Thumbnail_640x290-640x290.jpg');
													"
												></div>
												<span>Pour tout voir</span>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/caseStudies/WIDESCAPE/WS---K72.ca---MenuThumbnail-640x290.jpg');
													"
												></div>
												<span>Pour tout voir&nbsp;</span>
											</div>
										</div>
									</div>
								</a>
							</li>
							<li class="c-menu_main-nav_item">
								<a href="agence" class="c-menu_main-nav_link" data-main-nav="link">
									<span>Agence</span>
									<div class="c-menu_main-nav_link_overlay">
										<div class="c-menu_main-nav_link_overlay_inner">
											<div>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/teamMembers/CAMILLE_640X290_2-640x290.jpg');
													"
												></div>
												<span>Pour tout savoir</span>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/teamMembers/blank_copie_2-640x290.jpg');
													"
												></div>
												<span>Pour tout savoir&nbsp;</span>
											</div>
											<div>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/teamMembers/CAMILLE_640X290_2-640x290.jpg');
													"
												></div>
												<span>Pour tout savoir</span>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/teamMembers/blank_copie_2-640x290.jpg');
													"
												></div>
												<span>Pour tout savoir&nbsp;</span>
											</div>
										</div>
									</div>
								</a>
							</li>
							<li class="c-menu_main-nav_item">
								<a href="contact" class="c-menu_main-nav_link" data-main-nav="link">
									<span>Contact</span>
									<div class="c-menu_main-nav_link_overlay">
										<div class="c-menu_main-nav_link_overlay_inner">
											<div>
												<svg role="presentation" alt="" aria-hidden="true">
													<use xlink:href="assets/images/sprite.svg#heart"></use>
												</svg>
												<span>Pour envoyer un fax</span>
												<svg role="presentation" alt="" aria-hidden="true">
													<use xlink:href="assets/images/sprite.svg#heart"></use>
												</svg>
												<span>Pour envoyer un fax&nbsp;</span>
											</div>
											<div>
												<svg role="presentation" alt="" aria-hidden="true">
													<use xlink:href="assets/images/sprite.svg#heart"></use>
												</svg>
												<span>Pour envoyer un fax</span>
												<svg role="presentation" alt="" aria-hidden="true">
													<use xlink:href="assets/images/sprite.svg#heart"></use>
												</svg>
												<span>Pour envoyer un fax&nbsp;</span>
											</div>
										</div>
									</div>
								</a>
							</li>
							<li class="c-menu_main-nav_item">
								<a href="blogue" class="c-menu_main-nav_link" data-main-nav="link">
									<span>Blogue</span>
									<div class="c-menu_main-nav_link_overlay">
										<div class="c-menu_main-nav_link_overlay_inner">
											<div>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/blog/blogImg/ier.com-16107673482102220.gif');
													"
												></div>
												<span>Lire les articles</span>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/blog/blogImg/50ff59cc0550df5b36543807a58db98c52e01a22274a317eafbfa5266941579b-640x290.png');
													"
												></div>
												<span>Lire les articles&nbsp;</span>
											</div>
											<div>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/blog/blogImg/ier.com-16107673482102220.gif');
													"
												></div>
												<span>Lire les articles</span>
												<div
													class="c-pill-image"
													style="
														background-image: url('uploads/blog/blogImg/50ff59cc0550df5b36543807a58db98c52e01a22274a317eafbfa5266941579b-640x290.png');
													"
												></div>
												<span>Lire les articles&nbsp;</span>
											</div>
										</div>
									</div>
								</a>
							</li>
						</ul>

						<div class="c-menu_footer">
							<div class="c-menu_footer_clock">
								<div class="c-clock">
									<svg role="img" alt="Globe icon">
										<use xlink:href="assets/images/sprite.svg#globe"></use>
									</svg>
									<span>MONTREAL_<span data-module-time>22:30:24</span></span>
								</div>
							</div>
							<ul class="c-menu_footer_legals">
								<li>
									<a class="c-menu_footer_legals_link" href="politique-de-confidentialite"
										>Politique de confidentialité</a
									>
								</li>
								<li>
									<a class="c-menu_footer_legals_link" href="avis-de-confidentialite"
										>Avis de confidentialité</a
									>
								</li>
								<li>
									<a
										class="c-menu_footer_legals_link"
										href="https://secure.ethicspoint.com/domain/media/frca/gui/47632/index.html"
										target="_blank"
										rel="noopener noreferrer"
										>Rapport éthique</a
									>
								</li>
								<li>
									<button class="c-menu_footer_legals_link" onclick="Didomi.preferences.show()">
										Options de consentement
									</button>
								</li>
							</ul>
							<div class="c-menu_footer_socials">
								<ul class="c-socials">
									<li class="c-socials_item">
										<a
											href="https://www.facebook.com/K72.ca/"
											class="c-button || c-socials_link"
											target="_blank"
											rel="noopener noreferrer"
										>
											<span class="u-screen-reader-text">Facebook</span>
											<span aria-hidden="true">FB</span>
										</a>
									</li>
									<li class="c-socials_item">
										<a
											href="https://www.instagram.com/k72_creation/"
											class="c-button || c-socials_link"
											target="_blank"
											rel="noopener noreferrer"
										>
											<span class="u-screen-reader-text">Instagram</span>
											<span aria-hidden="true">IG</span>
										</a>
									</li>
									<li class="c-socials_item">
										<a
											href="https://www.linkedin.com/company/k72"
											class="c-button || c-socials_link"
											target="_blank"
											rel="noopener noreferrer"
										>
											<span class="u-screen-reader-text">Linkedin</span>
											<span aria-hidden="true">IN</span>
										</a>
									</li>
									<li class="c-socials_item">
										<a
											href="https://www.behance.net/agenceK72"
											class="c-button || c-socials_link"
											target="_blank"
											rel="noopener noreferrer"
										>
											<span class="u-screen-reader-text">Behance</span>
											<span aria-hidden="true">BE</span>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</nav>
			</header>
			<div class="c-home" data-module-home>
				<div class="c-home_content">
					<h1 class="c-home_punchline || c-text-lines is-inview">
						<div class="c-text-lines_item"><span class="c-text-lines_item_inner">L'étincelle</span></div>
						<div class="c-text-lines_item -allow-overflow u-z-index-2">
							<span class="c-text-lines_item_outer"><span class="c-text-lines_item_inner">qui</span></span
							><canvas class="c-home_monitor" data-module-monitor></canvas
							><span class="c-text-lines_item_outer"
								><span class="c-text-lines_item_inner">génère</span></span
							>
						</div>
						<div class="c-text-lines_item -allow-overflow">
							<span class="c-text-lines_item_outer"
								><span class="c-text-lines_item_inner"
									>la
									<mark
										><div class="c-circle" data-module-circle="home"></div>
										créativité</mark
									></span
								></span
							>
						</div>
					</h1>

					<p class="c-home_text || o-text -small || u-indent">
						K72 est une agence qui pense chaque action pour nourrir la marque. Demain, dans 5 mois et dans 5
						ans. On cherche la friction qui crée l’étincelle pour générer de l’émotion. Pour assurer une
						relation honnête, on est sans filtre, on dit ce qui doit être dit, on fait ce qui doit être
						fait.
					</p>

					<ul class="c-home_ctas">
						<li class="c-home_ctas_item">
							<a href="travail" class="c-home_ctas_link || c-button -thicker">Projets</a>
						</li>
						<li class="c-home_ctas_item">
							<a href="agence" class="c-home_ctas_link || c-button -thicker">Agence</a>
						</li>
					</ul>

					<div class="c-clock">
						<svg role="img" alt="Globe icon"><use xlink:href="assets/images/sprite.svg#globe"></use></svg>
						<span>MONTREAL_<span data-module-time>22:30:24</span></span>
					</div>
				</div>
				<figure
					class="c-home_background"
					style="background-image: url('uploads/vlcsnap-2021-04-21-16h11m49s996.jpg')"
				>
					<!-- External Vimeo video commented out for local development
					<video
						src="https://player.vimeo.com/progressive_redirect/playback/899938964/rendition/1080p/file.mp4?loc=external&amp;log_user=0&amp;signature=6a2cb0320d09d02dcfdfa2ad504c89a510e6d93d55e9060b1b55d680fa2e2be9"
						muted
						playsinline
						loop
						data-monitor="source"
						data-home="video"
						poster="uploads/vlcsnap-2021-04-21-16h11m49s996.jpg"
					></video>
					-->
				</figure>
			</div>
		</div>

		<div class="c-video-modal" data-module-video-modal>
			<div class="c-video-modal_bg" data-video-modal="close"></div>
			<button type="button" class="c-button || c-video-modal_close" data-video-modal="close">
				<span>Fermer</span>
			</button>

			<div class="c-video-modal_content">
				<div class="c-video-modal_inner" data-video-modal="inner"></div>
			</div>
		</div>

		<!-- External polyfill - Commented out for local development
		<script
			nomodule
			src="https://cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?features=Element.prototype.remove%2CElement.prototype.append%2Cfetch%2CCustomEvent%2CElement.prototype.matches%2CNodeList.prototype.forEach%2CAbortController"
			crossorigin="anonymous"
		></script>
		-->

		<!-- Main app script restored for full functionality -->
		<script src="assets/scripts/app.js?v=20240527152606" type="module" defer></script>
	</body>
</html>
